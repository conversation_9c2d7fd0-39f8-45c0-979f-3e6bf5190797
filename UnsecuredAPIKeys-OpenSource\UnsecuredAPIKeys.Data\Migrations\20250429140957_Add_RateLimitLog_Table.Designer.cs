﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using UnsecuredAPIKeys.Data;

#nullable disable

namespace UnsecuredAPIKeys.Data.Migrations
{
    [DbContext(typeof(DBContext))]
    [Migration("20250429140957_Add_RateLimitLog_Table")]
    partial class Add_RateLimitLog_Table
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.APIKey", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("ApiKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ApiType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("FirstFoundUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastCheckedUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("LastFoundUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastValidUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("SearchProvider")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("TimesDisplayed")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("APIKeys");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.ApplicationSetting", b =>
                {
                    b.Property<string>("Key")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Key");

                    b.ToTable("ApplicationSettings");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.RateLimitLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Endpoint")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<DateTime>("RequestTimeUtc")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("RateLimitLogs");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.RepoReference", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<long>("APIKeyId")
                        .HasColumnType("bigint");

                    b.Property<string>("ApiContentUrl")
                        .HasColumnType("text");

                    b.Property<string>("Branch")
                        .HasColumnType("text");

                    b.Property<string>("CodeContext")
                        .HasColumnType("text");

                    b.Property<string>("FileName")
                        .HasColumnType("text");

                    b.Property<string>("FilePath")
                        .HasColumnType("text");

                    b.Property<string>("FileSHA")
                        .HasColumnType("text");

                    b.Property<string>("FileURL")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("FoundUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<string>("Provider")
                        .HasColumnType("text");

                    b.Property<string>("RepoDescription")
                        .HasColumnType("text");

                    b.Property<long>("RepoId")
                        .HasColumnType("bigint");

                    b.Property<string>("RepoName")
                        .HasColumnType("text");

                    b.Property<string>("RepoOwner")
                        .HasColumnType("text");

                    b.Property<string>("RepoURL")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("SearchQueryId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("APIKeyId");

                    b.ToTable("RepoReferences");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.SearchProviderToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastUsedUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("SearchProvider")
                        .HasColumnType("integer");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("SearchProviderTokens");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.SearchQuery", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastSearchUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Query")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("SearchResultsCount")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("SearchQueries");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.RepoReference", b =>
                {
                    b.HasOne("UnsecuredAPIKeys.Data.Models.APIKey", null)
                        .WithMany("References")
                        .HasForeignKey("APIKeyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.APIKey", b =>
                {
                    b.Navigation("References");
                });
#pragma warning restore 612, 618
        }
    }
}
