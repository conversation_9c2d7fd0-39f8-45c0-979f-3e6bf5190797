# Example nginx configuration for Next.js static export with trailing slashes
# Add this to your site configuration in aaPanel

location / {
    # First attempt to serve request as file, then
    # as directory with index.html, then fall back to 404
    try_files $uri $uri/index.html $uri/ /404/index.html;
}

# Handle specific routes with trailing slashes
location ~ ^/(about|donated|leaderboard|pricing)/$ {
    try_files $uri/index.html /404/index.html;
}

# Serve static assets directly
location /_next/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# Security headers
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;

# Gzip compression
gzip on;
gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml application/javascript application/json;
gzip_vary on;
