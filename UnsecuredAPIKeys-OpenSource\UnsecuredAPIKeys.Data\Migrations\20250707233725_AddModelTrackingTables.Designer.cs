﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using UnsecuredAPIKeys.Data;

#nullable disable

namespace UnsecuredAPIKeys.Data.Migrations
{
    [DbContext(typeof(DBContext))]
    [Migration("20250707233725_AddModelTrackingTables")]
    partial class AddModelTrackingTables
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.APIKey", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("ApiKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ApiType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("FirstFoundUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastCheckedUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("LastFoundUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastValidUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("SearchProvider")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("TimesDisplayed")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ApiKey")
                        .HasDatabaseName("IX_APIKeys_ApiKey");

                    b.HasIndex("LastCheckedUTC")
                        .HasDatabaseName("IX_APIKeys_LastCheckedUTC");

                    b.HasIndex("Status", "ApiType")
                        .HasDatabaseName("IX_APIKeys_Status_ApiType");

                    b.ToTable("APIKeys");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.ApiKeyModel", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<long>("ApiKeyId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DiscoveredUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("HasAccess")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastVerifiedUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("ProviderModelId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("HasAccess")
                        .HasDatabaseName("IX_ApiKeyModels_HasAccess");

                    b.HasIndex("ProviderModelId");

                    b.HasIndex("ApiKeyId", "ProviderModelId")
                        .IsUnique()
                        .HasDatabaseName("IX_ApiKeyModels_ApiKeyId_ProviderModelId");

                    b.ToTable("ApiKeyModels");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.ApplicationSetting", b =>
                {
                    b.Property<string>("Key")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Key");

                    b.ToTable("ApplicationSettings");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.DiscordUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AccessToken")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Avatar")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("DiscordId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Discriminator")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("FirstLoginUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("HighestTier")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("IpLastUpdatedUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsServerMember")
                        .HasColumnType("boolean");

                    b.Property<string>("LastKnownIpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<DateTime>("LastLoginUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("LastMembershipCheckUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastRoleCheckUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastSeenUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("RateLimitOverride")
                        .HasColumnType("integer");

                    b.Property<string>("RefreshToken")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ServerRoles")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("TokenExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.ToTable("DiscordUsers");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.DonationTracking", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("ClickLocation")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("ClickedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("ConfirmedDonation")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("DonationAmount")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("DonationConfirmedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<string>("PayPalTransactionId")
                        .HasColumnType("text");

                    b.Property<string>("SessionId")
                        .HasColumnType("text");

                    b.Property<string>("UserAgent")
                        .HasColumnType("text");

                    b.Property<string>("UserIP")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ClickedAt")
                        .HasDatabaseName("IX_DonationTracking_ClickedAt");

                    b.HasIndex("ConfirmedDonation")
                        .HasDatabaseName("IX_DonationTracking_ConfirmedDonation");

                    b.HasIndex("UserIP", "ClickedAt")
                        .HasDatabaseName("IX_DonationTracking_UserIP_ClickedAt");

                    b.ToTable("DonationTrackings");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.IssueSubmissionTracking", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<long>("ApiKeyId")
                        .HasColumnType("bigint");

                    b.Property<string>("ApiType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("RepoUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserIP")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("IssueSubmissionTrackings");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.IssueVerification", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("FirstCheckedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("GitHubAvatarUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("GitHubDisplayName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<long?>("GitHubIssueNumber")
                        .HasColumnType("bigint");

                    b.Property<string>("GitHubIssueUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long?>("GitHubUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("GitHubUsername")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("IssueClosedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("IssueCreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("IssueSubmissionTrackingId")
                        .HasColumnType("integer");

                    b.Property<string>("IssueTitle")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("LastCheckedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RepoUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("SubmitterIP")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("IssueSubmissionTrackingId");

                    b.ToTable("IssueVerifications");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.KeyInvalidation", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<long>("ApiKeyId")
                        .HasColumnType("bigint");

                    b.Property<bool>("ConfirmedFixed")
                        .HasColumnType("boolean");

                    b.Property<int>("DaysActive")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("FixedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("HttpStatusCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("InvalidatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("InvalidationReason")
                        .HasColumnType("text");

                    b.Property<string>("PreviousStatus")
                        .HasColumnType("text");

                    b.Property<bool>("WasValid")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("ApiKeyId")
                        .HasDatabaseName("IX_KeyInvalidations_ApiKeyId");

                    b.HasIndex("InvalidatedAt")
                        .HasDatabaseName("IX_KeyInvalidations_InvalidatedAt");

                    b.ToTable("KeyInvalidations");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.KeyRotation", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<long>("NewKeyId")
                        .HasColumnType("bigint");

                    b.Property<int>("OldKeyDaysActive")
                        .HasColumnType("integer");

                    b.Property<long>("OldKeyId")
                        .HasColumnType("bigint");

                    b.Property<string>("RepoUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("RotatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("NewKeyId");

                    b.HasIndex("OldKeyId");

                    b.ToTable("KeyRotations");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.PatternEffectiveness", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("FirstSeen")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("InvalidKeys")
                        .HasColumnType("integer");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MostSuccessfulFileTypes")
                        .HasColumnType("text");

                    b.Property<string>("Pattern")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ProviderName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TotalMatches")
                        .HasColumnType("integer");

                    b.Property<int>("ValidKeys")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ProviderName")
                        .HasDatabaseName("IX_PatternEffectiveness_Provider");

                    b.ToTable("PatternEffectiveness");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.ProviderModel", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int>("ApiType")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("DeprecatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("FirstSeenUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("InputTokenLimit")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeprecated")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastSeenUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<float?>("MaxTemperature")
                        .HasColumnType("real");

                    b.Property<string>("ModelGroup")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ModelId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<long?>("OutputTokenLimit")
                        .HasColumnType("bigint");

                    b.Property<string>("SupportedMethods")
                        .HasColumnType("text");

                    b.Property<float?>("Temperature")
                        .HasColumnType("real");

                    b.Property<int?>("TopK")
                        .HasColumnType("integer");

                    b.Property<float?>("TopP")
                        .HasColumnType("real");

                    b.Property<string>("Version")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("ApiType")
                        .HasDatabaseName("IX_ProviderModels_ApiType");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ProviderModels_IsActive");

                    b.HasIndex("ApiType", "ModelId")
                        .IsUnique()
                        .HasDatabaseName("IX_ProviderModels_ApiType_ModelId");

                    b.ToTable("ProviderModels");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.Proxy", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("LastUsedUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ProxyUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Proxies");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.RateLimitLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Endpoint")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<DateTime>("RequestTimeUtc")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("RateLimitLogs");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.RepoReference", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<long>("APIKeyId")
                        .HasColumnType("bigint");

                    b.Property<string>("ApiContentUrl")
                        .HasColumnType("text");

                    b.Property<string>("Branch")
                        .HasColumnType("text");

                    b.Property<string>("CodeContext")
                        .HasColumnType("text");

                    b.Property<string>("FileName")
                        .HasColumnType("text");

                    b.Property<string>("FilePath")
                        .HasColumnType("text");

                    b.Property<string>("FileSHA")
                        .HasColumnType("text");

                    b.Property<string>("FileURL")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("FoundUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<string>("Provider")
                        .HasColumnType("text");

                    b.Property<string>("RepoDescription")
                        .HasColumnType("text");

                    b.Property<long>("RepoId")
                        .HasColumnType("bigint");

                    b.Property<string>("RepoName")
                        .HasColumnType("text");

                    b.Property<string>("RepoOwner")
                        .HasColumnType("text");

                    b.Property<string>("RepoURL")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("SearchQueryId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("APIKeyId")
                        .HasDatabaseName("IX_RepoReferences_ApiKeyId");

                    b.ToTable("RepoReferences");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.SearchProviderToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastUsedUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("SearchProvider")
                        .HasColumnType("integer");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("SearchProviderTokens");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.SearchQuery", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastSearchUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Query")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("SearchResultsCount")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("SearchQueries");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.SnitchLeaderboard", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int>("ClosedIssuesSubmitted")
                        .HasColumnType("integer");

                    b.Property<int>("ConsecutiveDaysActive")
                        .HasColumnType("integer");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("FavoriteApiType")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("FirstSubmissionAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("LastSubmissionAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("LastUpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("OpenIssuesSubmitted")
                        .HasColumnType("integer");

                    b.Property<double>("SnitchScore")
                        .HasColumnType("double precision");

                    b.Property<int>("TotalIssuesSubmitted")
                        .HasColumnType("integer");

                    b.Property<int>("TotalRepositoriesAffected")
                        .HasColumnType("integer");

                    b.Property<string>("UserIdentifier")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.ToTable("SnitchLeaderboards");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.UserBan", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BanType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("BannedAtUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("BannedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("DiscordUserId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ExpiresAtUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("SubnetMask")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAtUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("DiscordUserId");

                    b.ToTable("UserBans");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.UserSession", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CountryCode")
                        .HasMaxLength(2)
                        .HasColumnType("character varying(2)");

                    b.Property<int?>("DiscordUserId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("FirstSeenUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastSeenUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("RequestCount")
                        .HasColumnType("integer");

                    b.Property<string>("SessionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("DiscordUserId");

                    b.ToTable("UserSessions");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.VerificationBatch", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<long>("EndKeyId")
                        .HasColumnType("bigint");

                    b.Property<int?>("ErrorKeys")
                        .HasColumnType("integer");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<string>("InstanceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("InvalidKeys")
                        .HasColumnType("integer");

                    b.Property<int>("KeyCount")
                        .HasColumnType("integer");

                    b.Property<DateTime>("LockExpiresAtUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("LockedAtUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ProcessingCompletedAtUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ProcessingStartedAtUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("SkippedKeys")
                        .HasColumnType("integer");

                    b.Property<long>("StartKeyId")
                        .HasColumnType("bigint");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int?>("ValidKeys")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("InstanceId")
                        .HasDatabaseName("IX_VerificationBatch_InstanceId");

                    b.HasIndex("StartKeyId", "EndKeyId")
                        .HasDatabaseName("IX_VerificationBatch_KeyRange");

                    b.HasIndex("Status", "LockExpiresAtUTC")
                        .HasDatabaseName("IX_VerificationBatch_Status_LockExpires");

                    b.ToTable("VerificationBatches");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.VerificationBatchResult", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int>("BatchSize")
                        .HasColumnType("integer");

                    b.Property<int>("InvalidKeys")
                        .HasColumnType("integer");

                    b.Property<int>("SkippedKeys")
                        .HasColumnType("integer");

                    b.Property<double>("TimeTakenInMinutes")
                        .HasColumnType("double precision");

                    b.Property<int>("ValidKeys")
                        .HasColumnType("integer");

                    b.Property<DateTime>("VerificationDateUTC")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("VerificationBatchResults");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.ApiKeyModel", b =>
                {
                    b.HasOne("UnsecuredAPIKeys.Data.Models.APIKey", "ApiKey")
                        .WithMany("ApiKeyModels")
                        .HasForeignKey("ApiKeyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UnsecuredAPIKeys.Data.Models.ProviderModel", "ProviderModel")
                        .WithMany("ApiKeyModels")
                        .HasForeignKey("ProviderModelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApiKey");

                    b.Navigation("ProviderModel");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.IssueVerification", b =>
                {
                    b.HasOne("UnsecuredAPIKeys.Data.Models.IssueSubmissionTracking", "IssueSubmissionTracking")
                        .WithMany()
                        .HasForeignKey("IssueSubmissionTrackingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("IssueSubmissionTracking");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.KeyInvalidation", b =>
                {
                    b.HasOne("UnsecuredAPIKeys.Data.Models.APIKey", "ApiKey")
                        .WithMany()
                        .HasForeignKey("ApiKeyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApiKey");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.KeyRotation", b =>
                {
                    b.HasOne("UnsecuredAPIKeys.Data.Models.APIKey", "NewKey")
                        .WithMany()
                        .HasForeignKey("NewKeyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("UnsecuredAPIKeys.Data.Models.APIKey", "OldKey")
                        .WithMany()
                        .HasForeignKey("OldKeyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("NewKey");

                    b.Navigation("OldKey");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.RepoReference", b =>
                {
                    b.HasOne("UnsecuredAPIKeys.Data.Models.APIKey", null)
                        .WithMany("References")
                        .HasForeignKey("APIKeyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.UserBan", b =>
                {
                    b.HasOne("UnsecuredAPIKeys.Data.Models.DiscordUser", "DiscordUser")
                        .WithMany("Bans")
                        .HasForeignKey("DiscordUserId");

                    b.Navigation("DiscordUser");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.UserSession", b =>
                {
                    b.HasOne("UnsecuredAPIKeys.Data.Models.DiscordUser", "DiscordUser")
                        .WithMany("Sessions")
                        .HasForeignKey("DiscordUserId");

                    b.Navigation("DiscordUser");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.APIKey", b =>
                {
                    b.Navigation("ApiKeyModels");

                    b.Navigation("References");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.DiscordUser", b =>
                {
                    b.Navigation("Bans");

                    b.Navigation("Sessions");
                });

            modelBuilder.Entity("UnsecuredAPIKeys.Data.Models.ProviderModel", b =>
                {
                    b.Navigation("ApiKeyModels");
                });
#pragma warning restore 612, 618
        }
    }
}
