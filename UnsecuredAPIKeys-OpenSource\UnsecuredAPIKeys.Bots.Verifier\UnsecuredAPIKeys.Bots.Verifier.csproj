﻿<Project Sdk="Microsoft.NET.Sdk;Microsoft.NET.Sdk.Publish">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="LangChain" Version="0.17.0" />
    <PackageReference Include="LangChain.Providers.Anthropic" Version="0.17.0" />
    <PackageReference Include="LangChain.Providers.Google" Version="0.17.0" />
    <PackageReference Include="LangChain.Providers.OpenAI" Version="0.17.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\UnsecuredAPIKeys.Data\UnsecuredAPIKeys.Data.csproj" />
    <ProjectReference Include="..\UnsecuredAPIKeys.Providers\UnsecuredAPIKeys.Providers.csproj" />
  </ItemGroup>

</Project>
