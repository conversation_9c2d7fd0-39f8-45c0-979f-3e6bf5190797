{"name": "unsecuredapikeys-ui", "private": true, "version": "0.0.0", "description": "UI for UnsecuredAPIKeys - A comprehensive platform for discovering and validating unsecured API keys", "repository": {"type": "git", "url": "https://github.com/TSCarterJr/UnsecuredAPIKeys-OpenSource.git"}, "homepage": "https://github.com/TSCarterJr/UnsecuredAPIKeys-OpenSource", "bugs": {"url": "https://github.com/TSCarterJr/UnsecuredAPIKeys-OpenSource/issues"}, "license": "MIT", "scripts": {"dev": "next dev", "build": "node scripts/optimize-build.js && next build && node scripts/compress-assets.js", "build:simple": "next build", "build:optimized": "NODE_ENV=production node scripts/optimize-build.js && next build && node scripts/compress-assets.js", "start": "next start", "lint": "next lint", "optimize": "node scripts/optimize-build.js", "compress": "node scripts/compress-assets.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@heroui/button": "^2.2.17", "@heroui/card": "^2.2.20", "@heroui/checkbox": "^2.3.21", "@heroui/code": "2.2.12", "@heroui/divider": "^2.2.15", "@heroui/drawer": "^2.2.14", "@heroui/dropdown": "2.3.17", "@heroui/input": "2.4.17", "@heroui/kbd": "2.2.13", "@heroui/link": "2.2.14", "@heroui/navbar": "2.2.15", "@heroui/snippet": "2.2.18", "@heroui/switch": "2.2.15", "@heroui/system": "2.4.17", "@heroui/theme": "2.4.17", "@heroui/use-theme": "2.1.6", "@microsoft/signalr": "^8.0.7", "@next/third-parties": "^15.3.3", "@react-aria/visually-hidden": "3.8.21", "@react-types/shared": "3.28.0", "@sentry/nextjs": "^9.14.0", "@sentry/react": "^9.14.0", "clsx": "2.1.1", "framer-motion": "^11.15.0", "js-cookie": "^3.0.5", "next": "^14.2.0", "react": "18.3.1", "react-dom": "18.3.1", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.11.0", "@typescript-eslint/parser": "8.11.0", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "4.1.4", "postcss": "8.4.38", "prettier": "3.3.3", "typescript": "5.6.3"}}