{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "System": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=UnsecuredAPIKeys;Username=postgres;Password=your_password;Port=5432"}, "VerificationSettings": {"BatchSize": 100, "MaxConcurrency": 5, "DelayBetweenBatches": 30000, "MaxRetryAttempts": 3}, "ApiProviders": {"OpenAI": {"Enabled": true, "MaxRequestsPerMinute": 60}, "Anthropic": {"Enabled": true, "MaxRequestsPerMinute": 60}}}