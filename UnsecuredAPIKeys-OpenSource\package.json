{"name": "unsecuredapikeys-opensource", "version": "1.0.0", "description": "A comprehensive platform for discovering, validating, and tracking unsecured API keys across various code repositories and platforms", "repository": {"type": "git", "url": "https://github.com/TSCarterJr/UnsecuredAPIKeys-OpenSource.git"}, "homepage": "https://github.com/TSCarterJr/UnsecuredAPIKeys-OpenSource", "bugs": {"url": "https://github.com/TSCarterJr/UnsecuredAPIKeys-OpenSource/issues"}, "license": "MIT", "author": "TSCarterJr", "private": true, "dependencies": {"@sentry/nextjs": "^9.35.0", "@sentry/react": "^9.35.0", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "next": "^15.3.5", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3"}}