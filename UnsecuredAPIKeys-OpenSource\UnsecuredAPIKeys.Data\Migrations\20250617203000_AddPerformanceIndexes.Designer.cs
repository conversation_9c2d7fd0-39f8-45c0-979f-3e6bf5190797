// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using UnsecuredAPIKeys.Data;

#nullable disable

namespace UnsecuredAPIKeys.Data.Migrations
{
    [DbContext(typeof(DBContext))]
    [Migration("20250617203000_AddPerformanceIndexes")]
    partial class AddPerformanceIndexes
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.0");

            // Note: This is a performance indexes migration only
            // The full model definition remains unchanged from the previous migration
            // Only new indexes are added to improve query performance
#pragma warning restore 612, 618
        }
    }
}