{"name": "Unsecured API Keys", "short_name": "UnsecuredAPIKeys", "description": "Help developers avoid that awkward conversation with their boss. Find and report exposed API keys in public GitHub repositories.", "icons": [{"src": "/lock_icon.svg", "sizes": "any", "type": "image/svg+xml"}, {"src": "/lock_icon.svg", "sizes": "192x192", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "/lock_icon.svg", "sizes": "512x512", "type": "image/svg+xml", "purpose": "any maskable"}], "theme_color": "#000000", "background_color": "#ffffff", "display": "standalone", "start_url": "/", "scope": "/", "orientation": "portrait-primary"}