# Minimal nginx configuration to fix trailing slash issue
# Add this to your existing nginx site configuration in aaPanel

# Main fix for trailing slash URLs
location / {
    try_files $uri $uri/index.html $uri/ =404;
}

# Alternative approach if the above doesn't work:
# This explicitly handles each route
location = /leaderboard/ {
    try_files /leaderboard/index.html =404;
}

location = /about/ {
    try_files /about/index.html =404;
}

location = /pricing/ {
    try_files /pricing/index.html =404;
}

location = /donated/ {
    try_files /donated/index.html =404;
}
